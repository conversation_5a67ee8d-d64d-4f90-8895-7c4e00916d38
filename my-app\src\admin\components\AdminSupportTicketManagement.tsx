/* eslint-disable no-case-declarations */
import {
  AlertCircle,
  CheckCircle,
  Clock,
  MessageCircle,
  Search,
} from "lucide-react";
import { useState } from "react";
import {
  SupportTicketStatus,
  type SupportTicket,
} from "../../components/support/types";
import api from "../../utils/api";
import { useToast } from "../../utils/ToastNotification";
import { dummyTickets } from "../helpers/dummyData";
import { TicketFilters } from "./Support/Filter";
import { TicketTable } from "./Support/TicketTable";
import { ViewTicketModal } from "./Support/ViewTicketModal";

export default function SupportTicketsManagement() {
  const [tickets, setTickets] = useState<SupportTicket[]>(dummyTickets);
  const [viewTicket, setViewTicket] = useState<SupportTicket | null>(null);
  const [isPageLoading, setIsPageLoading] = useState<boolean>(false);
  const [isReplyLoading, setIsRepylyLoading] = useState<boolean>(false);
  const [isVeiwMoreLoading, setIsViewMoreLoading] = useState<boolean>(false);
  const [isStatusChangeLoading, setIsStatusChangeLoading] =
    useState<boolean>(false);
  const [isCloseTicketLoading, setIsCloseTicketLoading] =
    useState<boolean>(false);
  const toast = useToast();

  const [filters, setFilters] = useState({
    search: "",
    status: "",
    category: "",
    dateRange: "",
  });

  // Filter logic
  // const filteredTickets = useMemo(() => {
  //   return tickets.filter((ticket) => {
  //     // Search filter
  // }, [tickets, filters]);

  const handleFilterChange = (key: string, value: string) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleClearFilters = () => {
    setFilters({
      search: "",
      status: "",
      category: "",
      dateRange: "",
    });
  };

  const handleView = async (ticketId: string) => {
    setIsViewMoreLoading(true);
    try {
      const request = await api.get(`/support/ticket/${ticketId}`);
      const response = request.data;
      setViewTicket(response.data);
    } catch (error: any) {
      console.error("Error submitting ticket", error);

      if (error.response) {
        toast.error(error.response.data.message || "Something went wrong");
      } else if (
        error.code === "ERR_NETWORK" ||
        error.code === "ECONNABORTED" ||
        error.message.includes("Network Error")
      ) {
        window.dispatchEvent(new CustomEvent("network-error"));
      } else {
        toast.error("Unexpected error occurred.");
      }
    } finally {
      setIsViewMoreLoading(false);
    }
  };

  const handleReply = async (ticketId: string, message: string) => {
    setIsRepylyLoading(true);
    try {
      await api.post(`/support/ticket/reply/${ticketId}`);

      setTickets((prev) =>
        prev.map((ticket) =>
          ticket._id === ticketId
            ? { ...ticket, lastUpdated: new Date().toISOString().split("T")[0] }
            : ticket
        )
      );
    } catch (error: any) {
      console.error("Error replying ticket", error);

      if (error.response) {
        toast.error(error.response.data.message || "Something went wrong");
      } else if (
        error.code === "ERR_NETWORK" ||
        error.code === "ECONNABORTED" ||
        error.message.includes("Network Error")
      ) {
        window.dispatchEvent(new CustomEvent("network-error"));
      } else {
        toast.error("Unexpected error occurred.");
      }
    } finally {
      setIsRepylyLoading(false);
    }
  };

  const handleStatusChange = async (
    ticketId: string,
    newStatus: SupportTicket["status"]
  ) => {
    setIsStatusChangeLoading(true);
    try {
      await api.patch(`/support/ticket/status/${ticketId}`);
      setTickets((prev) =>
        prev.map((ticket) =>
          ticket._id === ticketId
            ? {
                ...ticket,
                status: newStatus,
                lastUpdated: new Date().toISOString().split("T")[0],
              }
            : ticket
        )
      );
    } catch (error: any) {
      console.error("Error updating ticket status", error);

      if (error.response) {
        toast.error(error.response.data.message || "Something went wrong");
      } else if (
        error.code === "ERR_NETWORK" ||
        error.code === "ECONNABORTED" ||
        error.message.includes("Network Error")
      ) {
        window.dispatchEvent(new CustomEvent("network-error"));
      } else {
        toast.error("Unexpected error occurred.");
      }
    } finally {
      setIsStatusChangeLoading(false);
    }
  };

  const getStatusCounts = () => {
    return tickets.reduce((acc, ticket) => {
      acc[ticket.status] = (acc[ticket.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  };

  const statusCounts = getStatusCounts();

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2" style={{ color: "#263b51" }}>
          Support Tickets
        </h1>
        <p className="text-gray-600">
          Manage customer support requests and inquiries
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div
          className="bg-white rounded-lg border p-6"
          style={{ borderColor: "#CBDCEB" }}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Open Tickets</p>
              <p className="text-2xl font-bold" style={{ color: "#263b51" }}>
                {statusCounts.open || 0}
              </p>
            </div>
            <AlertCircle className="w-8 h-8 text-blue-500" />
          </div>
        </div>

        <div
          className="bg-white rounded-lg border p-6"
          style={{ borderColor: "#CBDCEB" }}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">In Progress</p>
              <p className="text-2xl font-bold" style={{ color: "#263b51" }}>
                {statusCounts.in_progress || 0}
              </p>
            </div>
            <Clock className="w-8 h-8 text-yellow-500" />
          </div>
        </div>

        <div
          className="bg-white rounded-lg border p-6"
          style={{ borderColor: "#CBDCEB" }}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Resolved</p>
              <p className="text-2xl font-bold" style={{ color: "#263b51" }}>
                {statusCounts.resolved || 0}
              </p>
            </div>
            <CheckCircle className="w-8 h-8 text-green-500" />
          </div>
        </div>

        <div
          className="bg-white rounded-lg border p-6"
          style={{ borderColor: "#CBDCEB" }}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Tickets</p>
              <p className="text-2xl font-bold" style={{ color: "#263b51" }}>
                {filteredTickets.length}
              </p>
            </div>
            <MessageCircle className="w-8 h-8" style={{ color: "#456882" }} />
          </div>
        </div>
      </div>

      {/* Filters */}
      <TicketFilters
        filters={filters}
        onFilterChange={handleFilterChange}
        onClearFilters={handleClearFilters}
        ticketCounts={statusCounts}
      />

      {/* Results Summary */}
      {(filters.search ||
        filters.status ||
        filters.category ||
        filters.dateRange) && (
        <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-sm" style={{ color: "#263b51" }}>
            Showing{" "}
            <span className="font-semibold">{filteredTickets.length}</span> of{" "}
            <span className="font-semibold">{tickets.length}</span> tickets
            {filters.search && (
              <span>
                {" "}
                matching "
                <span className="font-semibold">{filters.search}</span>"
              </span>
            )}
          </p>
        </div>
      )}

      {/* Tickets Table */}
      <TicketTable
        tickets={filteredTickets}
        onView={handleView}
        onReply={handleReply}
        onStatusChange={handleStatusChange}
        onClose={(ticketId) => {
          handleStatusChange(ticketId, SupportTicketStatus.CLOSED);
        }}
      />

      {/* Empty State */}
      {filteredTickets.length === 0 && tickets.length > 0 && (
        <div className="text-center py-12">
          <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
            <Search className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No tickets found
          </h3>
          <p className="text-gray-500 mb-4">
            Try adjusting your search criteria or clearing the filters.
          </p>
          <button
            onClick={handleClearFilters}
            className="px-4 py-2 text-white rounded-md"
            style={{ backgroundColor: "#456882" }}
          >
            Clear Filters
          </button>
        </div>
      )}

      {/* View Modal */}
      <ViewTicketModal
        ticket={viewTicket}
        onClose={() => setViewTicket(null)}
      />
    </div>
  );
}
